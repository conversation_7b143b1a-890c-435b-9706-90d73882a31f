// Authentication module
class AuthManager {
    constructor() {
        this.currentUser = null;
        this.authToken = null;
        this.isAuthenticated = false;
        
        // Initialize authentication state
        this.init();
    }

    async init() {
        try {
            // Check for stored authentication data
            const storedToken = localStorage.getItem(appConfig.storageKeys.authToken);
            const storedUser = localStorage.getItem(appConfig.storageKeys.user);

            if (storedToken && storedUser) {
                this.authToken = storedToken;
                this.currentUser = JSON.parse(storedUser);
                this.isAuthenticated = true;

                // Verify token is still valid
                const isValid = await this.verifyToken();
                if (!isValid) {
                    this.logout();
                    return;
                }

                this.showDashboard();
            } else {
                this.showAuth();
            }

            // Set up Supabase auth state listener
            if (window.supabaseClient) {
                window.supabaseClient.auth.onAuthStateChange((event, session) => {
                    if (event === 'SIGNED_IN' && session && !this.isAuthenticated) {
                        // User signed in through Supabase
                        this.handleSupabaseAuth(session);
                    } else if (event === 'SIGNED_OUT' && this.isAuthenticated) {
                        // User signed out
                        this.logout();
                    }
                });
            }
        } catch (error) {
            console.error('Auth initialization error:', error);
            this.showAuth();
        }
    }

    async handleFirebaseAuth(firebaseUser) {
        try {
            showLoading();
            
            // Get Firebase ID token
            const idToken = await firebaseUser.getIdToken();
            
            // Send to backend for verification and user data
            const response = await fetch(`${apiConfig.baseURL}/auth/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ idToken })
            });

            const data = await response.json();

            if (data.success) {
                this.setAuthData(data.data.user, data.data.jwtToken);
                this.showDashboard();
                showToast('Welcome back!', 'success');
            } else {
                throw new Error(data.message);
            }
        } catch (error) {
            console.error('Firebase auth error:', error);
            showToast(error.message || 'Authentication failed', 'error');
            this.logout();
        } finally {
            hideLoading();
        }
    }

    async handleSupabaseAuth(session) {
        try {
            showLoading();

            // Get user data from session
            const user = session.user;

            // Set authentication data
            this.setAuthData({
                id: user.id,
                email: user.email,
                username: user.user_metadata?.username || user.email.split('@')[0],
                first_name: user.user_metadata?.first_name || '',
                last_name: user.user_metadata?.last_name || '',
                user_type: user.user_metadata?.user_type || 'student'
            }, session.access_token);

            this.showDashboard();
            showToast('Welcome back!', 'success');
        } catch (error) {
            console.error('Supabase auth error:', error);
            showToast(error.message || 'Authentication failed', 'error');
            this.logout();
        } finally {
            hideLoading();
        }
    }

    async login(email, password) {
        try {
            showLoading();

            // Sign in with Supabase
            const { data, error } = await window.supabaseClient.auth.signInWithPassword({
                email: email,
                password: password
            });

            if (error) {
                throw error;
            }

            if (data.user && data.session) {
                // Set authentication data
                this.setAuthData({
                    id: data.user.id,
                    email: data.user.email,
                    username: data.user.user_metadata?.username || data.user.email.split('@')[0],
                    first_name: data.user.user_metadata?.first_name || '',
                    last_name: data.user.user_metadata?.last_name || '',
                    user_type: data.user.user_metadata?.user_type || 'student'
                }, data.session.access_token);

                this.showDashboard();
                showToast(appConfig.successMessages.login, 'success');
                return true;
            } else {
                throw new Error('Login failed');
            }
        } catch (error) {
            console.error('Login error:', error);
            let errorMessage = 'Login failed';

            if (error.message === 'Invalid login credentials') {
                errorMessage = 'Invalid email or password';
            } else if (error.message === 'Email not confirmed') {
                errorMessage = 'Please check your email and confirm your account';
            } else if (error.message) {
                errorMessage = error.message;
            }

            showToast(errorMessage, 'error');
            return false;
        } finally {
            hideLoading();
        }
    }

    async register(userData) {
        try {
            showLoading();

            // Create user with Supabase
            const { data, error } = await window.supabaseClient.auth.signUp({
                email: userData.email,
                password: userData.password,
                options: {
                    data: {
                        first_name: userData.firstName,
                        last_name: userData.lastName,
                        username: userData.username || userData.email.split('@')[0],
                        user_type: 'student'
                    }
                }
            });

            if (error) {
                throw error;
            }

            if (data.user) {
                // Also create user in our custom table
                const { data: dbResult, error: dbError } = await window.supabaseClient
                    .rpc('create_user_account', {
                        p_email: userData.email,
                        p_password: userData.password, // In production, this should be hashed
                        p_first_name: userData.firstName,
                        p_last_name: userData.lastName,
                        p_username: userData.username,
                        p_phone_number: userData.phoneNumber
                    });

                if (dbError) {
                    console.error('Database user creation error:', dbError);
                }

                showToast('Registration successful! Please check your email to confirm your account.', 'success');
                return true;
            } else {
                throw new Error('Registration failed');
            }
        } catch (error) {
            console.error('Registration error:', error);
            let errorMessage = 'Registration failed';
            
            if (error.code === 'auth/email-already-in-use') {
                errorMessage = 'An account with this email already exists';
            } else if (error.code === 'auth/weak-password') {
                errorMessage = 'Password is too weak';
            } else if (error.code === 'auth/invalid-email') {
                errorMessage = 'Invalid email address';
            } else if (error.message) {
                errorMessage = error.message;
            }
            
            showToast(errorMessage, 'error');
            return false;
        } finally {
            hideLoading();
        }
    }

    async logout() {
        try {
            showLoading();

            // Sign out from Supabase
            const { error } = await window.supabaseClient.auth.signOut();
            if (error) {
                console.error('Logout error:', error);
            }

            this.clearAuthData();
            this.showAuth();
            showToast(appConfig.successMessages.logout, 'success');
        } catch (error) {
            console.error('Logout error:', error);
            // Clear data anyway
            this.clearAuthData();
            this.showAuth();
        } finally {
            hideLoading();
        }
    }

    async forgotPassword(email) {
        try {
            showLoading();

            await window.auth.sendPasswordResetEmail(email);
            showToast('Password reset email sent. Check your inbox.', 'success');
            return true;
        } catch (error) {
            console.error('Forgot password error:', error);
            let errorMessage = 'Failed to send password reset email';
            
            if (error.code === 'auth/user-not-found') {
                errorMessage = 'No account found with this email';
            } else if (error.code === 'auth/invalid-email') {
                errorMessage = 'Invalid email address';
            }
            
            showToast(errorMessage, 'error');
            return false;
        } finally {
            hideLoading();
        }
    }

    async updateProfile(profileData) {
        try {
            showLoading();

            const response = await fetch(`${apiConfig.baseURL}/auth/profile`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.authToken}`
                },
                body: JSON.stringify(profileData)
            });

            const data = await response.json();

            if (data.success) {
                this.currentUser = data.data.user;
                localStorage.setItem(appConfig.storageKeys.user, JSON.stringify(this.currentUser));
                showToast(appConfig.successMessages.profileUpdate, 'success');
                return true;
            } else {
                throw new Error(data.message);
            }
        } catch (error) {
            console.error('Profile update error:', error);
            showToast(error.message || 'Failed to update profile', 'error');
            return false;
        } finally {
            hideLoading();
        }
    }

    async verifyToken() {
        try {
            const response = await fetch(`${apiConfig.baseURL}/auth/profile`, {
                headers: {
                    'Authorization': `Bearer ${this.authToken}`
                }
            });

            return response.ok;
        } catch (error) {
            console.error('Token verification error:', error);
            return false;
        }
    }

    setAuthData(user, token) {
        this.currentUser = user;
        this.authToken = token;
        this.isAuthenticated = true;

        // Store in localStorage
        localStorage.setItem(appConfig.storageKeys.user, JSON.stringify(user));
        localStorage.setItem(appConfig.storageKeys.authToken, token);
    }

    clearAuthData() {
        this.currentUser = null;
        this.authToken = null;
        this.isAuthenticated = false;

        // Clear localStorage
        localStorage.removeItem(appConfig.storageKeys.user);
        localStorage.removeItem(appConfig.storageKeys.authToken);
    }

    showAuth() {
        document.getElementById('auth-container').classList.remove('hidden');
        document.getElementById('dashboard-container').classList.add('hidden');
        document.getElementById('navbar').classList.add('hidden');
    }

    showDashboard() {
        document.getElementById('auth-container').classList.add('hidden');
        document.getElementById('dashboard-container').classList.remove('hidden');
        document.getElementById('navbar').classList.remove('hidden');

        // Update navbar with user info
        this.updateNavbar();

        // Load dashboard content
        if (window.dashboardManager) {
            window.dashboardManager.loadDashboard();
        }
    }

    updateNavbar() {
        if (this.currentUser) {
            const userNameElement = document.getElementById('user-name');
            const userAvatarElement = document.getElementById('user-avatar');

            if (userNameElement) {
                userNameElement.textContent = this.currentUser.firstName || 'User';
            }

            if (userAvatarElement && this.currentUser.avatarUrl) {
                userAvatarElement.src = this.currentUser.avatarUrl;
            }

            // Show/hide navigation items based on role
            const studentsNav = document.getElementById('nav-students');
            if (studentsNav) {
                if (this.currentUser.role === 'instructor' || this.currentUser.role === 'admin') {
                    studentsNav.classList.remove('hidden');
                } else {
                    studentsNav.classList.add('hidden');
                }
            }
        }
    }

    getAuthHeaders() {
        return {
            'Authorization': `Bearer ${this.authToken}`,
            'Content-Type': 'application/json'
        };
    }

    hasRole(role) {
        return this.currentUser && this.currentUser.role === role;
    }

    hasAnyRole(roles) {
        return this.currentUser && roles.includes(this.currentUser.role);
    }
}

// Initialize auth manager
window.authManager = new AuthManager();
